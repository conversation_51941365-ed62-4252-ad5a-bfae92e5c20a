import { Card, Table, THead, Th, TB<PERSON>, Td, But<PERSON> } from "@/components/ui";

export default function ContactsPage() {
  const contacts = [
    { id: 1, name: "<PERSON>", email: "<EMAIL>", status: "Lead" },
    { id: 2, name: "<PERSON>", email: "<EMAIL>", status: "Customer" },
  ];

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-2xl font-bold">Contacts</h1>
        <Button>Add Contact</Button>
      </div>

      <Card>
        <Table>
          <THead>
            <Th>Name</Th>
            <Th>Email</Th>
            <Th>Status</Th>
          </THead>
          <TBody>
            {contacts.map((c) => (
              <tr key={c.id}>
                <Td>{c.name}</Td>
                <Td>{c.email}</Td>
                <Td>{c.status}</Td>
              </tr>
            ))}
          </TBody>
        </Table>
      </Card>
    </div>
  );
}