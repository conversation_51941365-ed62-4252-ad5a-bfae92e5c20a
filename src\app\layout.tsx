import type { Metada<PERSON> } from "next";
import { Inter } from "next/font/google";
import "./globals.css";
import { Providers } from "./providers";

const inter = Inter({ subsets: ["latin"] });

export const metadata: Metadata = {
  title: "CRM Dashboard",
  description: "Modern Next.js 15 CRM Template",
};

export default function RootLayout({
  children,
}: Readonly<{ children: React.ReactNode }>) {
  return (
    <html lang="en" suppressHydrationWarning>
      {/* ❗  no className here – ThemeProvider will add it */}
      <body className={inter.className}>
        <Providers>{children}</Providers>
      </body>
    </html>
  );
}