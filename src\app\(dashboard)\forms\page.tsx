"use client";
import { useState } from "react";
import { Card, Input, Button, Switch, Checkbox, Radio, Dropdown } from "@/components/ui";

export default function FormsPage() {
  const [checked, setChecked] = useState(false);
  const [radio, setRadio] = useState("a");
  return (
    <div className="space-y-6">
      <h1 className="text-2xl font-bold">Forms & Components</h1>

      <Card className="p-6 grid grid-cols-1 md:grid-cols-2 gap-6">
        <Input placeholder="Text input" />
        <Input type="email" placeholder="Email input" />
        <div className="flex items-center gap-2">
          <span className="text-sm">Toggle</span>
          <Switch checked={checked} onChange={setChecked} />
        </div>
        <div className="flex items-center gap-2">
          <Checkbox checked={checked} onChange={setChecked} />
          <span className="text-sm">Checkbox</span>
        </div>
        <div className="flex items-center gap-4">
          <label className="flex items-center gap-2">
            <Radio checked={radio === "a"} onChange={() => setRadio("a")} />
            A
          </label>
          <label className="flex items-center gap-2">
            <Radio checked={radio === "b"} onChange={() => setRadio("b")} />
            B
          </label>
        </div>
        <Dropdown label="Actions" options={["Edit", "Delete", "Export"]} />
      </Card>
    </div>
  );
}