import { <PERSON><PERSON>, <PERSON> } from "@/components/ui";
import { Plus } from "@/components/ui/Icons";

export default function DashboardHome() {
  return (
    <div className="space-y-6">
      <h1 className="text-2xl font-bold">Dashboard</h1>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        {Array.from({ length: 3 }).map((_, i) => (
          <Card key={i} className="p-6">
            <h3 className="font-semibold mb-2">Metric {i + 1}</h3>
            <p className="text-3xl font-bold text-brand-600">
              {(Math.random() * 1000).toFixed(0)}
            </p>
          </Card>
        ))}
      </div>

      <Card className="p-6">
        <div className="flex items-center justify-between mb-4">
          <h2 className="text-lg font-semibold">Recent Activity</h2>
          <Button>
            <Plus className="w-4 h-4 mr-2" /> Add Activity
          </Button>
        </div>
        <p className="text-slate-500 dark:text-slate-400">
          No recent activities to display.
        </p>
      </Card>
    </div>
  );
}